struct Rectangle {
    width: u32,
    height: u32,
}

impl Rectangle {
    fn can_hold(&self, other: &Rectangle) -> bool {
        self.width > other.width && self.height > other.height
    }
}

impl Rectangle {
    fn can_begin(&self, other: &Rectangle) -> Option<bool> {
        Some(self.width > other.width && self.height > other.height)
    }
}

fn main() {
    let mut counter = 0;
    let test1 = Rectangle {
        width: 1,
        height: 2,
    };

    let test2 = Rectangle {
        width: 3,
        height: 5,
    };

    let value = test2.can_begin(&test1);

    match value {
        Some(v) => println!("Some Some"),
        None => println!("None None"),
    }

    println!("xxx {}", counter);

    let result = loop {
        counter += 1;

        if counter == 10 {
            break counter * 2;
        }
    };

    println!("The result is {}", result);
    println!("The result is {}", test1.height);
}
